# GitHub Actions Deployment Guide

This guide explains how to deploy the Voice Assistant API using GitHub Actions CI/CD pipelines.

## 🚀 Quick Start

### 1. Setup Repository Secrets

Go to your GitHub repository → Settings → Secrets and variables → Actions

#### Required Secrets:
```
VOICE_API_KEY=your-secure-api-key-here
```

#### Optional Secrets:
```
OPENAI_API_KEY=sk-proj-your-openai-key
DATABASE_URL=********************************/db
SENTRY_DSN=https://<EMAIL>/project
```

### 2. Setup Environments

Go to Settings → Environments and create:

#### Staging Environment
- **Name**: `staging`
- **Deployment branches**: `develop`, `feature/*`
- **Required reviewers**: 0
- **Wait timer**: 0 minutes

#### Production Environment  
- **Name**: `production`
- **Deployment branches**: `main` only
- **Required reviewers**: 1
- **Wait timer**: 5 minutes

### 3. Configure Environment Variables

For each environment, add these variables:

#### Staging Variables:
```
RAG_MODEL_NAME=gpt-4o
TRANSCRIPTION_MODEL=gpt-4o-transcribe
ENABLE_TTS=true
LOG_LEVEL=INFO
CORS_ORIGINS=["http://localhost:3000", "https://staging.yourapp.com"]
```

#### Production Variables:
```
RAG_MODEL_NAME=gpt-4o
TRANSCRIPTION_MODEL=gpt-4o-transcribe
ENABLE_TTS=true
LOG_LEVEL=WARNING
CORS_ORIGINS=["https://yourapp.com", "https://www.yourapp.com"]
```

## 🔄 Deployment Workflows

### Automatic Deployments

#### 1. **CI/CD Pipeline** (`.github/workflows/ci-cd.yml`)
- **Triggers**: Push to `main`/`develop`, Pull Requests, Releases
- **Actions**: 
  - Run tests on multiple Python versions
  - Security scanning with Trivy
  - Build and push Docker images to GitHub Container Registry
  - Auto-deploy to staging on `develop` branch
  - Auto-deploy to production on releases

#### 2. **Build and Test** (`.github/workflows/build-and-test.yml`)
- **Triggers**: Push to any branch, PRs, Daily schedule
- **Actions**:
  - Code linting (Black, flake8, isort)
  - Cross-platform testing (Ubuntu, Windows, macOS)
  - Docker build testing
  - Integration tests

### Manual Deployments

#### 3. **Manual Deployment** (`.github/workflows/manual-deploy.yml`)
- **Trigger**: Manual workflow dispatch
- **Options**:
  - Choose environment (staging/production)
  - Select deployment method (docker-compose/kubernetes/cloud-run/ec2)
  - Specify image tag
  - Use simple or full Docker image
  - Skip tests option

#### 4. **Docker Compose Deployment** (`.github/workflows/deploy-docker-compose.yml`)
- **Trigger**: Manual workflow dispatch
- **Actions**: Deploy using Docker Compose with environment-specific configuration

## 🐳 Docker Images

The CI/CD pipeline builds two Docker images:

### Full Image (`ghcr.io/your-username/era-agents:tag`)
- Contains all dependencies including audio processing
- Suitable for full-featured deployments
- Larger size (~1GB)

### Simple Image (`ghcr.io/your-username/era-agents-simple:tag`)
- Minimal dependencies for API-only deployments
- Smaller size (~200MB)
- Perfect for cloud deployments

## 📋 Deployment Methods

### 1. Docker Compose (Recommended)

```bash
# Automatic via GitHub Actions
git push origin develop  # Deploys to staging
git tag v1.0.0 && git push origin v1.0.0  # Deploys to production

# Manual via GitHub Actions
# Go to Actions → Manual Deployment → Run workflow
```

### 2. Kubernetes

```yaml
# Add your Kubernetes manifests to k8s/ directory
# Update manual-deploy.yml with kubectl commands
```

### 3. Cloud Run (Google Cloud)

```bash
# Add GCP service account key to secrets
# Update manual-deploy.yml with gcloud commands
```

### 4. EC2 (AWS)

```bash
# Add AWS credentials to secrets
# Update manual-deploy.yml with deployment script
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `VOICE_API_KEY` | API authentication key | - | ✅ |
| `RAG_MODEL_NAME` | LLM model for RAG | `gpt-4o` | ❌ |
| `TRANSCRIPTION_MODEL` | Speech-to-text model | `gpt-4o-transcribe` | ❌ |
| `ENABLE_TTS` | Enable text-to-speech | `true` | ❌ |
| `LOG_LEVEL` | Logging level | `INFO` | ❌ |
| `CORS_ORIGINS` | Allowed CORS origins | `["*"]` | ❌ |

### Secrets Management

#### Repository Secrets (Global)
- Used across all environments
- Store sensitive data like API keys
- Access: `${{ secrets.SECRET_NAME }}`

#### Environment Secrets (Environment-specific)
- Override repository secrets for specific environments
- Higher precedence than repository secrets
- Access: `${{ secrets.SECRET_NAME }}`

#### Environment Variables (Non-sensitive)
- Store configuration that can be public
- Environment-specific settings
- Access: `${{ vars.VARIABLE_NAME }}`

## 🔍 Monitoring & Debugging

### Workflow Logs
- Go to Actions tab in your repository
- Click on any workflow run to see detailed logs
- Each job and step shows execution details

### Deployment Health Checks
All deployments include automatic health checks:
- API health endpoint: `GET /health`
- API documentation: `GET /docs`
- Authenticated status: `GET /status` (with API key)

### Common Issues

#### 1. **Image Not Found**
```bash
# Check available images
curl -s "https://api.github.com/repos/YOUR_USERNAME/era-agents/packages/container/era-agents/versions" | jq -r '.[].metadata.container.tags[]'
```

#### 2. **Permission Denied**
- Ensure `GITHUB_TOKEN` has package write permissions
- Check repository settings → Actions → General → Workflow permissions

#### 3. **Environment Not Found**
- Create environments in Settings → Environments
- Add required secrets and variables

#### 4. **Deployment Timeout**
- Increase health check timeout in workflow
- Check service logs for startup issues

## 🔒 Security Best Practices

### 1. API Keys
- Use strong, unique API keys for each environment
- Rotate keys regularly
- Never commit keys to code

### 2. Environment Isolation
- Use separate secrets for staging/production
- Restrict production deployments to main branch
- Require reviews for production deployments

### 3. Image Security
- Trivy security scanning on all builds
- Regular base image updates
- Non-root user in containers

### 4. Network Security
- Configure CORS appropriately
- Use HTTPS in production
- Implement rate limiting

## 📊 Monitoring

### GitHub Actions
- Workflow success/failure notifications
- Build time and resource usage
- Security scan results

### Application Monitoring
- Health check endpoints
- Structured logging
- Optional: Sentry integration for error tracking

## 🚀 Next Steps

1. **Setup Repository**: Add secrets and environment variables
2. **Test Deployment**: Push to develop branch to test staging deployment
3. **Production Release**: Create a release tag for production deployment
4. **Monitor**: Check workflow logs and application health
5. **Scale**: Add more deployment targets (Kubernetes, Cloud providers)

## 📞 Support

For issues with GitHub Actions deployment:
1. Check workflow logs in Actions tab
2. Verify secrets and environment configuration
3. Test Docker images locally
4. Review this documentation for troubleshooting steps

## 🎯 Example Deployment Flow

### Staging Deployment
```bash
# 1. Create feature branch
git checkout -b feature/new-feature

# 2. Make changes and commit
git add .
git commit -m "Add new feature"

# 3. Push to trigger CI
git push origin feature/new-feature

# 4. Create PR to develop
# GitHub Actions will run tests

# 5. Merge to develop
# Automatic deployment to staging environment
```

### Production Deployment
```bash
# 1. Merge develop to main
git checkout main
git merge develop
git push origin main

# 2. Create release tag
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# 3. Automatic deployment to production
# (after approval if required reviewers are set)
```

### Manual Deployment
1. Go to GitHub repository
2. Click "Actions" tab
3. Select "Manual Deployment" workflow
4. Click "Run workflow"
5. Choose environment and options
6. Click "Run workflow" button
