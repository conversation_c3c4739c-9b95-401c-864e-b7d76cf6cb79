name: Build and Test

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install linting tools
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy
    
    - name: <PERSON> Black (code formatting)
      run: |
        black --check --diff .
      continue-on-error: true
    
    - name: Run isort (import sorting)
      run: |
        isort --check-only --diff .
      continue-on-error: true
    
    - name: Run flake8 (linting)
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
      continue-on-error: true

  test-matrix:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.11', '3.12']
        include:
          - os: ubuntu-latest
            python-version: '3.11'
            test-type: 'full'
          - os: windows-latest
            python-version: '3.11'
            test-type: 'api-only'
          - os: macos-latest
            python-version: '3.11'
            test-type: 'api-only'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/pip
          ~/.cache/pipenv
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.python-version }}-
          ${{ runner.os }}-pip-
    
    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y portaudio19-dev python3-dev ffmpeg
    
    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install portaudio ffmpeg
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        
        # Install based on test type
        if [ "${{ matrix.test-type }}" = "full" ]; then
          pip install -r requirements.txt || pip install -r requirements-api.txt
        else
          pip install -r requirements-api.txt
        fi
        
        # Install test dependencies
        pip install pytest pytest-cov pytest-asyncio httpx
      shell: bash
    
    - name: Test dependency installation
      run: |
        python start_api_only.py --check
    
    - name: Create test files if missing
      run: |
        mkdir -p tests
        
        # Create comprehensive test file
        cat > tests/test_comprehensive.py << 'EOF'
import pytest
import sys
import os
import importlib.util

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_python_version():
    """Test Python version compatibility"""
    assert sys.version_info >= (3, 11), "Python 3.11+ required"

def test_core_imports():
    """Test that core modules can be imported"""
    modules_to_test = [
        'api_server',
        'websocket_voice_server', 
        'utils',
        'security',
        'questionnaire_handler'
    ]
    
    for module_name in modules_to_test:
        try:
            spec = importlib.util.find_spec(module_name)
            if spec is not None:
                module = importlib.import_module(module_name)
                print(f"✅ Successfully imported {module_name}")
            else:
                print(f"⚠️ Module {module_name} not found")
        except Exception as e:
            print(f"⚠️ Failed to import {module_name}: {e}")

def test_config_files():
    """Test that configuration files exist"""
    config_files = [
        'config/default.ini',
        'forms.json',
        'questionnaire_config.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ Found {config_file}")
        else:
            print(f"⚠️ Missing {config_file}")

@pytest.mark.asyncio
async def test_api_server_creation():
    """Test that API server can be created"""
    try:
        from api_server import app
        assert app is not None
        print("✅ API server app created successfully")
    except ImportError as e:
        pytest.skip(f"API server not available: {e}")
    except Exception as e:
        pytest.fail(f"Failed to create API server: {e}")

def test_docker_files():
    """Test that Docker files exist and are valid"""
    docker_files = ['Dockerfile', 'Dockerfile.simple', 'docker-compose.yml']
    
    for docker_file in docker_files:
        assert os.path.exists(docker_file), f"Missing {docker_file}"
        
        with open(docker_file, 'r') as f:
            content = f.read()
            assert len(content) > 0, f"{docker_file} is empty"
            print(f"✅ {docker_file} exists and has content")
EOF
      shell: bash
    
    - name: Run tests
      run: |
        python -m pytest tests/ -v --tb=short
      continue-on-error: ${{ matrix.test-type != 'full' }}
    
    - name: Test API server startup (API-only)
      if: matrix.test-type == 'api-only'
      run: |
        timeout 30s python start_api_only.py &
        sleep 10
        curl -f http://localhost:8000/health || echo "API not ready yet"
      shell: bash
      continue-on-error: true

  docker-build-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Test Docker build (main)
      run: |
        docker build -t voice-assistant-api:test .
    
    - name: Test Docker build (simple)
      run: |
        docker build -f Dockerfile.simple -t voice-assistant-api-simple:test .
    
    - name: Test Docker run (main)
      run: |
        docker run -d --name test-api -p 8000:8000 voice-assistant-api:test
        sleep 15
        curl -f http://localhost:8000/health || (docker logs test-api && exit 1)
        docker stop test-api
        docker rm test-api
    
    - name: Test Docker run (simple)
      run: |
        docker run -d --name test-api-simple -p 8001:8000 voice-assistant-api-simple:test
        sleep 15
        curl -f http://localhost:8001/health || (docker logs test-api-simple && exit 1)
        docker stop test-api-simple
        docker rm test-api-simple

  integration-test:
    runs-on: ubuntu-latest
    needs: [test-matrix, docker-build-test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run integration tests with Docker Compose
      run: |
        # Start services
        docker-compose up -d
        
        # Wait for services
        sleep 30
        
        # Test endpoints
        curl -f http://localhost:8000/health
        curl -f http://localhost:8000/docs
        
        # Cleanup
        docker-compose down
