# GitHub Environment Configuration for Staging
# This file documents the required environment variables and secrets for staging deployment

name: staging
description: "Staging environment for Voice Assistant API"

# Environment Variables (set in GitHub repository settings > Environments > staging)
variables:
  # Application Configuration
  RAG_MODEL_NAME: "gpt-4o"
  TRANSCRIPTION_MODEL: "gpt-4o-transcribe"
  ENABLE_TTS: "true"
  LOG_LEVEL: "INFO"
  
  # CORS Configuration
  CORS_ORIGINS: '["http://localhost:3000", "https://staging.yourapp.com"]'
  
  # Resource Limits
  MEMORY_LIMIT: "1G"
  CPU_LIMIT: "0.5"

# Required Secrets (set in GitHub repository settings > Environments > staging > Secrets)
secrets:
  # API Authentication
  VOICE_API_KEY: "your-staging-api-key-here"
  
  # Optional: External API Keys
  OPENAI_API_KEY: "sk-proj-your-openai-key"
  
  # Optional: Database/Storage
  DATABASE_URL: "**************************************/voiceapi"
  
  # Optional: Monitoring
  SENTRY_DSN: "https://<EMAIL>/project"

# Deployment Protection Rules
protection_rules:
  required_reviewers: 0
  wait_timer: 0
  prevent_self_review: false

# Deployment Branches
deployment_branches:
  - develop
  - feature/*
