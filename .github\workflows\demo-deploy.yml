name: Demo Deployment

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'main'
        type: choice
        options:
        - main
        - develop
      
      use_simple_image:
        description: 'Use minimal Docker image'
        required: false
        default: false
        type: boolean
      
      port:
        description: 'Port to run on'
        required: false
        default: '8000'
      
      enable_nginx:
        description: 'Enable Nginx reverse proxy'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  deploy-demo:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        ref: ${{ inputs.branch }}
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Determine image to use
      run: |
        IMAGE_SUFFIX=""
        if [ "${{ inputs.use_simple_image }}" = "true" ]; then
          IMAGE_SUFFIX="-simple"
        fi
        
        DEMO_IMAGE="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}${IMAGE_SUFFIX}:${{ inputs.branch }}"
        echo "DEMO_IMAGE=$DEMO_IMAGE" >> $GITHUB_ENV
        echo "🐳 Using image: $DEMO_IMAGE"
    
    - name: Create demo deployment
      run: |
        echo "🚀 Setting up demo deployment..."
        
        # Create deployment directory
        mkdir -p demo-deployment
        cp docker-compose.yml demo-deployment/
        
        # Copy nginx config if needed
        if [ "${{ inputs.enable_nginx }}" = "true" ] && [ -f "nginx.conf" ]; then
          cp nginx.conf demo-deployment/
        fi
        
        # Create demo environment configuration
        cat > demo-deployment/.env << EOF
        # Demo Environment Configuration
        # Generated: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        # Branch: ${{ inputs.branch }}
        # No API key required for demo
        
        # Docker Configuration
        DOCKER_IMAGE=${DEMO_IMAGE}
        
        # Demo API Configuration (open access)
        VOICE_API_API_KEY=demo-no-auth-required
        VOICE_API_API_HOST=0.0.0.0
        VOICE_API_API_PORT=${{ inputs.port }}
        VOICE_API_WEBSOCKET_HOST=localhost
        VOICE_API_WEBSOCKET_PORT=8765
        
        # Open CORS for demo (allow all origins)
        VOICE_API_CORS_ORIGINS=["*"]
        
        # Application Configuration
        PYTHONPATH=/app
        LOG_LEVEL=INFO
        USE_SIMPLE_MODE=${{ inputs.use_simple_image }}
        
        # Demo-friendly settings
        RAG_MODEL_NAME=gpt-4o
        TRANSCRIPTION_MODEL=gpt-4o-transcribe
        ENABLE_TTS=true
        EOF
        
        cd demo-deployment
        
        # Update docker-compose.yml for demo
        # Replace build with image
        sed -i "s|build: \.|image: ${DEMO_IMAGE}|g" docker-compose.yml
        
        # Update port mapping if different from default
        if [ "${{ inputs.port }}" != "8000" ]; then
          sed -i "s|8000:8000|${{ inputs.port }}:8000|g" docker-compose.yml
        fi
        
        # Add environment file reference
        if ! grep -q "env_file:" docker-compose.yml; then
          sed -i '/environment:/i\    env_file:\n      - .env' docker-compose.yml
        fi
        
        # Enable nginx profile if requested
        if [ "${{ inputs.enable_nginx }}" = "true" ]; then
          echo "" >> docker-compose.yml
          echo "# Nginx enabled for demo" >> docker-compose.yml
        fi
        
        echo "✅ Demo deployment configured"
    
    - name: Test image availability
      run: |
        echo "🔍 Checking if image exists..."
        if docker pull "${DEMO_IMAGE}"; then
          echo "✅ Image found and pulled successfully"
        else
          echo "❌ Image not found: ${DEMO_IMAGE}"
          echo ""
          echo "Available images for this repository:"
          curl -s "https://api.github.com/repos/${{ github.repository }}/packages/container/${{ github.event.repository.name }}/versions" \
            | jq -r '.[].metadata.container.tags[]' | head -10 || echo "Could not fetch available tags"
          exit 1
        fi
    
    - name: Start demo deployment
      run: |
        cd demo-deployment
        
        echo "🚀 Starting demo deployment..."
        echo "Image: ${DEMO_IMAGE}"
        echo "Port: ${{ inputs.port }}"
        echo "Branch: ${{ inputs.branch }}"
        
        # Start the services
        if [ "${{ inputs.enable_nginx }}" = "true" ]; then
          docker-compose --profile production up -d
        else
          docker-compose up -d
        fi
        
        # Wait for services to start
        echo "⏳ Waiting for services to start..."
        sleep 30
        
        # Check if service is running
        if docker-compose ps | grep -q "Up"; then
          echo "✅ Services are running"
          docker-compose ps
        else
          echo "❌ Services failed to start"
          docker-compose logs
          exit 1
        fi
    
    - name: Run demo health checks
      run: |
        cd demo-deployment
        
        echo "🏥 Running health checks..."
        
        # Wait a bit more for full startup
        sleep 15
        
        # Test health endpoint
        if curl -f "http://localhost:${{ inputs.port }}/health"; then
          echo "✅ Health check passed"
        else
          echo "⚠️ Health check failed, checking logs..."
          docker-compose logs voice-assistant-api
        fi
        
        # Test API documentation
        if curl -f "http://localhost:${{ inputs.port }}/docs"; then
          echo "✅ API docs accessible"
        else
          echo "⚠️ API docs not accessible"
        fi
        
        echo ""
        echo "🎯 Demo is ready!"
        echo "🌐 API: http://localhost:${{ inputs.port }}"
        echo "📚 Docs: http://localhost:${{ inputs.port }}/docs"
        echo "🔌 WebSocket: ws://localhost:${{ inputs.port }}/ws/voice"
    
    - name: Generate demo instructions
      run: |
        cd demo-deployment
        
        cat > DEMO_INSTRUCTIONS.md << EOF
        # Voice Assistant API Demo
        
        ## 🚀 Quick Start
        
        This demo deployment is ready to use! No API key required.
        
        ### Access Points
        - **API Base URL**: http://localhost:${{ inputs.port }}
        - **API Documentation**: http://localhost:${{ inputs.port }}/docs
        - **Health Check**: http://localhost:${{ inputs.port }}/health
        - **WebSocket**: ws://localhost:${{ inputs.port }}/ws/voice
        
        ### Demo Configuration
        - **Branch**: ${{ inputs.branch }}
        - **Image**: ${DEMO_IMAGE}
        - **Simple Mode**: ${{ inputs.use_simple_image }}
        - **Nginx**: ${{ inputs.enable_nginx }}
        
        ## 🧪 Testing the API
        
        ### 1. Health Check
        \`\`\`bash
        curl http://localhost:${{ inputs.port }}/health
        \`\`\`
        
        ### 2. API Documentation
        Open http://localhost:${{ inputs.port }}/docs in your browser
        
        ### 3. WebSocket Test
        \`\`\`javascript
        const ws = new WebSocket('ws://localhost:${{ inputs.port }}/ws/voice');
        ws.onopen = () => console.log('Connected');
        ws.onmessage = (event) => console.log('Received:', event.data);
        \`\`\`
        
        ## 🛠 Management Commands
        
        ### View Logs
        \`\`\`bash
        docker-compose logs -f
        \`\`\`
        
        ### Stop Demo
        \`\`\`bash
        docker-compose down
        \`\`\`
        
        ### Restart Demo
        \`\`\`bash
        docker-compose restart
        \`\`\`
        
        ## 📊 Monitoring
        
        - Container status: \`docker-compose ps\`
        - Resource usage: \`docker stats\`
        - Service logs: \`docker-compose logs voice-assistant-api\`
        
        ---
        
        **Generated**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        **Deployment**: GitHub Actions Demo
        EOF
        
        echo "📋 Demo instructions created"
    
    - name: Upload demo package
      uses: actions/upload-artifact@v3
      with:
        name: voice-assistant-demo-${{ inputs.branch }}-${{ github.run_number }}
        path: demo-deployment/
        retention-days: 7
    
    - name: Demo summary
      run: |
        echo ""
        echo "🎉 Demo deployment completed successfully!"
        echo "=================================="
        echo "Branch: ${{ inputs.branch }}"
        echo "Image: ${DEMO_IMAGE}"
        echo "Port: ${{ inputs.port }}"
        echo "Simple Mode: ${{ inputs.use_simple_image }}"
        echo ""
        echo "🎯 Next Steps:"
        echo "1. Download the demo package from the Actions artifacts"
        echo "2. Extract and run: docker-compose up -d"
        echo "3. Access the API at: http://localhost:${{ inputs.port }}"
        echo "4. View documentation at: http://localhost:${{ inputs.port }}/docs"
        echo ""
        echo "📦 Demo package includes:"
        echo "- docker-compose.yml (configured for demo)"
        echo "- .env (demo environment variables)"
        echo "- DEMO_INSTRUCTIONS.md (complete setup guide)"
