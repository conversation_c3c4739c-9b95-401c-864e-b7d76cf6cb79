# GitHub Environment Configuration for Production
# This file documents the required environment variables and secrets for production deployment

name: production
description: "Production environment for Voice Assistant API"

# Environment Variables (set in GitHub repository settings > Environments > production)
variables:
  # Application Configuration
  RAG_MODEL_NAME: "gpt-4o"
  TRANSCRIPTION_MODEL: "gpt-4o-transcribe"
  ENABLE_TTS: "true"
  LOG_LEVEL: "WARNING"
  
  # CORS Configuration (restrict to your domains)
  CORS_ORIGINS: '["https://yourapp.com", "https://www.yourapp.com"]'
  
  # Resource Limits
  MEMORY_LIMIT: "2G"
  CPU_LIMIT: "1.0"
  
  # Performance Settings
  WORKERS: "4"
  MAX_CONNECTIONS: "1000"

# Required Secrets (set in GitHub repository settings > Environments > production > Secrets)
secrets:
  # API Authentication (use strong, unique key)
  VOICE_API_KEY: "your-production-api-key-here"
  
  # External API Keys
  OPENAI_API_KEY: "sk-proj-your-production-openai-key"
  
  # Database/Storage
  DATABASE_URL: "***********************************/voiceapi"
  
  # Monitoring & Logging
  SENTRY_DSN: "https://<EMAIL>/project"
  
  # Optional: SSL/TLS Certificates
  SSL_CERT_PATH: "/etc/ssl/certs/yourapp.crt"
  SSL_KEY_PATH: "/etc/ssl/private/yourapp.key"
  
  # Optional: Cloud Provider Credentials
  AWS_ACCESS_KEY_ID: "your-aws-access-key"
  AWS_SECRET_ACCESS_KEY: "your-aws-secret-key"
  
  # Optional: Container Registry
  DOCKER_REGISTRY_TOKEN: "your-registry-token"

# Deployment Protection Rules
protection_rules:
  required_reviewers: 1
  wait_timer: 5  # 5 minute wait before deployment
  prevent_self_review: true

# Deployment Branches (only main branch can deploy to production)
deployment_branches:
  - main
